// hooks/useGameResponseValidator.ts
import { useCallback, useMemo, useRef } from 'react';

// ========== TYPES ==========
export type GameResponseType = "yes" | "no" | "maybe" | "unknown" | "invalid";

export interface ValidationResult {
  type: GameResponseType;
  confidence: number;
  alternatives: GameResponseType[];
  suggestions?: string[];
  isAmbiguous: boolean;
}

export interface ResponsePattern {
  exact: string[];
  fuzzy: RegExp[];
  confidence: number;
}

// ========== ADVANCED PATTERN MATCHER ==========
class AdvancedResponseMatcher {
  private exactPatterns = new Map<string, GameResponseType>();
  private fuzzyPatterns: Array<{ pattern: RegExp; type: GameResponseType; confidence: number }> = [];
  private contextualPatterns = new Map<string, GameResponseType>();

  constructor() {
    this.initializePatterns();
  }

  private initializePatterns() {
    // Patrones exactos por idioma
    const exactPatterns: Record<GameResponseType, string[]> = {
      yes: [
        // Español
        "sí", "si", "claro", "correcto", "exacto", "cierto", "verdad",
        "afirmativo", "por supuesto", "efectivamente", "así es", "obvio",
        "desde luego", "sin duda", "seguro", "definitivamente", "por descontado",
        "lógicamente", "naturalmente", "indudablemente",
      ],
      no: [
        // Español
        "no", "nope", "negativo", "incorrecto", "falso", "para nada",
        "jamás", "nunca", "de ninguna manera", "en absoluto", "ni hablar",
        "imposible", "ni de coña", "qué va", "ni de broma", "ni loco",
        "para nada", "de ningún modo", "ni pensarlo",
      ],
      maybe: [
        // Español
        "tal vez", "quizás", "quizá", "puede ser", "posible", "posiblemente",
        "probablemente", "es posible", "podría ser", "a veces", "depende",
        "más o menos", "regular", "ni sí ni no", "50/50", "medio",
        "a medias", "en parte", "en cierto modo", "hasta cierto punto",
      ],
      unknown: [
        // Español
        "no sé", "no lo sé", "no tengo idea", "desconozco", "ni idea",
        "no estoy seguro", "no sabría decir", "no tengo ni idea",
        "ni la menor idea", "vete a saber", "quién sabe", "no tengo claro",
        "me suena", "no me acuerdo", "no caigo", "no me viene",
      ],
      invalid: []
    };

    // Poblar mapa de patrones exactos
    for (const [type, patterns] of Object.entries(exactPatterns)) {
      patterns.forEach(pattern => {
        this.exactPatterns.set(pattern.toLowerCase(), type as GameResponseType);
      });
    }

    // Patrones fuzzy con expresiones regulares más sofisticadas
    this.fuzzyPatterns = [
      // Afirmaciones
      { pattern: /^(sí|si|yes|claro|obvio)[\s.,!]*$/i, type: "yes", confidence: 0.95 },
      { pattern: /^(vale|okay|ok)[\s.,!]*$/i, type: "yes", confidence: 0.85 },
      { pattern: /(correcto|exacto|cierto|verdad)/i, type: "yes", confidence: 0.9 },
      { pattern: /(por\s*supuesto|desde\s*luego|sin\s*duda)/i, type: "yes", confidence: 0.9 },

      // Negaciones
      { pattern: /^no[\s.,!]*$/i, type: "no", confidence: 0.95 },
      { pattern: /(para\s*nada|de\s*ninguna\s*manera|en\s*absoluto)/i, type: "no", confidence: 0.9 },
      { pattern: /(jamás|nunca|imposible)/i, type: "no", confidence: 0.85 },
      { pattern: /(ni\s*hablar|ni\s*de\s*coña|qué\s*va)/i, type: "no", confidence: 0.8 },

      // Incertidumbre
      { pattern: /(tal\s*vez|quizás?|puede\s*ser)/i, type: "maybe", confidence: 0.9 },
      { pattern: /(posible|probablemente|podría\s*ser)/i, type: "maybe", confidence: 0.8 },
      { pattern: /(depende|a\s*veces|más\s*o\s*menos)/i, type: "maybe", confidence: 0.7 },

      // Desconocimiento
      { pattern: /(no\s*(lo\s*)?sé|ni\s*idea)/i, type: "unknown", confidence: 0.9 },
      { pattern: /(no\s*tengo\s*idea|desconozco)/i, type: "unknown", confidence: 0.85 },
      { pattern: /(no\s*estoy\s*seguro|no\s*sabría)/i, type: "unknown", confidence: 0.8 },
      { pattern: /(vete\s*a\s*saber|quién\s*sabe)/i, type: "unknown", confidence: 0.7 },
    ];

    // Patrones contextuales (para casos especiales)
    this.contextualPatterns.set("bueno", "maybe");
    this.contextualPatterns.set("pues", "maybe");
    this.contextualPatterns.set("ehh", "unknown");
    this.contextualPatterns.set("mmm", "maybe");
  }

  validate(text: string): ValidationResult {
    if (!text?.trim()) {
      return {
        type: "invalid",
        confidence: 0,
        alternatives: [],
        suggestions: ["Intenta decir: Sí, No, Tal vez, o No lo sé"],
        isAmbiguous: false
      };
    }

    const normalized = text.toLowerCase().trim();
    const results: Array<{ type: GameResponseType; confidence: number; source: string }> = [];

    // 1. Búsqueda exacta (máxima prioridad)
    const exactMatch = this.exactPatterns.get(normalized);
    if (exactMatch) {
      return {
        type: exactMatch,
        confidence: 1.0,
        alternatives: [],
        isAmbiguous: false
      };
    }

    // 2. Búsqueda fuzzy con regex
    for (const { pattern, type, confidence } of this.fuzzyPatterns) {
      if (pattern.test(normalized)) {
        results.push({ type, confidence, source: 'fuzzy' });
      }
    }

    // 3. Búsqueda contextual
    for (const [context, type] of this.contextualPatterns.entries()) {
      if (normalized.includes(context)) {
        results.push({ type, confidence: 0.5, source: 'contextual' });
      }
    }

    // 4. Búsqueda por inclusión (menor prioridad)
    for (const [pattern, type] of this.exactPatterns.entries()) {
      if (normalized.includes(pattern) && pattern.length > 2) {
        results.push({ type, confidence: 0.4, source: 'inclusion' });
      }
    }

    if (results.length === 0) {
      return {
        type: "invalid",
        confidence: 0,
        alternatives: [],
        suggestions: this.generateSuggestions(normalized),
        isAmbiguous: false
      };
    }

    // Procesar resultados
    const grouped = this.groupResultsByType(results);
    const sorted = Array.from(grouped.entries())
      .map(([type, items]) => ({
        type,
        confidence: Math.max(...items.map(i => i.confidence)),
        count: items.length
      }))
      .sort((a, b) => {
        // Priorizar por confianza, luego por cantidad de matches
        if (Math.abs(a.confidence - b.confidence) < 0.1) {
          return b.count - a.count;
        }
        return b.confidence - a.confidence;
      });

    const best = sorted[0];
    const alternatives = sorted.slice(1).map(r => r.type);
    const isAmbiguous = sorted.length > 1 &&
      Math.abs(sorted[0].confidence - sorted[1].confidence) < 0.3;

    return {
      type: best.type,
      confidence: best.confidence,
      alternatives,
      suggestions: isAmbiguous ? this.generateDisambiguationSuggestions(sorted) : undefined,
      isAmbiguous
    };
  }

  private groupResultsByType(results: Array<{ type: GameResponseType; confidence: number; source: string }>) {
    const grouped = new Map<GameResponseType, Array<{ confidence: number; source: string }>>();

    for (const result of results) {
      if (!grouped.has(result.type)) {
        grouped.set(result.type, []);
      }
      grouped.get(result.type)!.push({ confidence: result.confidence, source: result.source });
    }

    return grouped;
  }

  private generateSuggestions(text: string): string[] {
    const suggestions = ["Intenta decir más claramente:"];

    // Análisis básico del texto para sugerir
    if (text.includes("s") || text.includes("af") || text.includes("pos")) {
      suggestions.push("• 'Sí' o 'Claro'");
    }
    if (text.includes("n") || text.includes("neg")) {
      suggestions.push("• 'No' o 'Para nada'");
    }
    if (text.includes("vez") || text.includes("qui")) {
      suggestions.push("• 'Tal vez' o 'Quizás'");
    }

    suggestions.push("• 'No lo sé' si no estás seguro");

    return suggestions;
  }

  private generateDisambiguationSuggestions(sorted: Array<{ type: GameResponseType; confidence: number }>): string[] {
    const typeLabels = {
      yes: "Sí",
      no: "No",
      maybe: "Tal vez",
      unknown: "No lo sé",
      invalid: "Inválido"
    };

    return [
      "Tu respuesta es ambigua. ¿Querías decir:",
      ...sorted.slice(0, 2).map(s => `• '${typeLabels[s.type]}'?`)
    ];
  }
}

// ========== CUSTOM HOOK ==========
export const useGameResponseValidator = () => {
  const matcherRef = useRef(new AdvancedResponseMatcher());

  // ========== CORE VALIDATION ==========
  const validateResponse = useCallback((text: string): ValidationResult => {
    return matcherRef.current.validate(text);
  }, []);

  // ========== QUICK VALIDATION ==========
  const isValidResponse = useCallback((text: string): boolean => {
    const result = validateResponse(text);
    return result.type !== "invalid" && result.confidence > 0.6;
  }, [validateResponse]);

  const getResponseType = useCallback((text: string): GameResponseType => {
    const result = validateResponse(text);
    return result.confidence > 0.6 ? result.type : "invalid";
  }, [validateResponse]);

  // ========== CONFIDENCE & ALTERNATIVES ==========
  const getConfidenceScore = useCallback((text: string): number => {
    const result = validateResponse(text);
    return result.confidence;
  }, [validateResponse]);

  const getAlternatives = useCallback((text: string): GameResponseType[] => {
    const result = validateResponse(text);
    return result.alternatives;
  }, [validateResponse]);

  const isAmbiguous = useCallback((text: string): boolean => {
    const result = validateResponse(text);
    return result.isAmbiguous;
  }, [validateResponse]);

  // ========== SUGGESTIONS & HELP ==========
  const getSuggestions = useCallback((text: string): string[] => {
    const result = validateResponse(text);
    return result.suggestions || [];
  }, [validateResponse]);

  const getSupportedResponses = useMemo(() => [
    { type: "yes" as const, examples: ["Sí", "Claro", "Correcto", "Por supuesto"] },
    { type: "no" as const, examples: ["No", "Para nada", "Jamás", "En absoluto"] },
    { type: "maybe" as const, examples: ["Tal vez", "Quizás", "Puede ser", "Depende"] },
    { type: "unknown" as const, examples: ["No lo sé", "No tengo idea", "Desconozco", "Ni idea"] }
  ], []);

  const getResponseHelp = useCallback((): string => {
    return "Responde con claridad usando: Sí, No, Tal vez, o No lo sé (también acepto variaciones)";
  }, []);

  const getDetailedHelp = useMemo(() => ({
    title: "Cómo responder en Enygma",
    sections: [
      {
        title: "✅ Para confirmar",
        examples: ["Sí", "Claro", "Correcto", "Por supuesto", "Efectivamente"]
      },
      {
        title: "❌ Para negar",
        examples: ["No", "Para nada", "En absoluto", "Jamás", "Imposible"]
      },
      {
        title: "🤔 Si no estás seguro",
        examples: ["Tal vez", "Quizás", "Puede ser", "A veces", "Depende"]
      },
      {
        title: "❓ Si no lo sabes",
        examples: ["No lo sé", "No tengo idea", "Desconozco", "Ni idea"]
      }
    ],
    tips: [
      "Habla con claridad y usa palabras simples",
      "Evita frases muy largas o complejas",
      "Si hay ruido, repite tu respuesta",
      "Puedes usar sinónimos o variaciones"
    ]
  }), []);

  // ========== BATCH VALIDATION ==========
  const validateMultiple = useCallback((texts: string[]): ValidationResult[] => {
    return texts.map(text => validateResponse(text));
  }, [validateResponse]);

  const getBestMatch = useCallback((texts: string[]): ValidationResult | null => {
    const results = validateMultiple(texts);
    const valid = results.filter(r => r.type !== "invalid");

    if (valid.length === 0) return null;

    return valid.reduce((best, current) =>
      current.confidence > best.confidence ? current : best
    );
  }, [validateMultiple]);

  // ========== TESTING UTILITIES ==========
  const testResponse = useCallback((text: string): {
    input: string;
    result: ValidationResult;
    passed: boolean;
    message: string;
  } => {
    const result = validateResponse(text);
    const passed = result.type !== "invalid" && result.confidence > 0.6;

    let message = "";
    if (passed) {
      message = `✅ Reconocido como "${result.type}" con ${(result.confidence * 100).toFixed(1)}% confianza`;
    } else if (result.isAmbiguous) {
      message = `⚠️ Respuesta ambigua. Alternativas: ${result.alternatives.join(", ")}`;
    } else {
      message = `❌ No reconocido. ${result.suggestions?.join(" ") || "Intenta ser más claro."}`;
    }

    return { input: text, result, passed, message };
  }, [validateResponse]);

  const runTestSuite = useCallback(() => {
    const testCases = [
      // Casos positivos
      { input: "sí", expected: "yes" },
      { input: "claro que sí", expected: "yes" },
      { input: "no", expected: "no" },
      { input: "para nada", expected: "no" },
      { input: "tal vez", expected: "maybe" },
      { input: "no lo sé", expected: "unknown" },

      // Casos edge
      { input: "síííí", expected: "yes" },
      { input: "nope", expected: "no" },
      { input: "ni idea", expected: "unknown" },
      { input: "puede", expected: "maybe" },

      // Casos difíciles
      { input: "ehh bueno", expected: "maybe" },
      { input: "pues no sé", expected: "unknown" },
      { input: "gibberish", expected: "invalid" }
    ];

    return testCases.map(({ input, expected }) => {
      const result = testResponse(input);
      const matches = result.result.type === expected;

      return {
        ...result,
        expected,
        matches,
        status: matches ? "✅ PASS" : "❌ FAIL"
      };
    });
  }, [testResponse]);

  // ========== ANALYTICS ==========
  const getValidationStats = useCallback((texts: string[]) => {
    const results = validateMultiple(texts);
    const stats = {
      total: results.length,
      valid: results.filter(r => r.type !== "invalid").length,
      ambiguous: results.filter(r => r.isAmbiguous).length,
      byType: {} as Record<GameResponseType, number>,
      averageConfidence: 0
    };

    // Contar por tipo
    results.forEach(result => {
      stats.byType[result.type] = (stats.byType[result.type] || 0) + 1;
    });

    // Calcular confianza promedio
    const validResults = results.filter(r => r.type !== "invalid");
    if (validResults.length > 0) {
      stats.averageConfidence = validResults.reduce((sum, r) => sum + r.confidence, 0) / validResults.length;
    }

    return stats;
  }, [validateMultiple]);

  // ========== RETURN HOOK API ==========
  return {
    // Core validation
    validateResponse,
    isValidResponse,
    getResponseType,

    // Confidence & alternatives
    getConfidenceScore,
    getAlternatives,
    isAmbiguous,

    // Suggestions & help
    getSuggestions,
    getSupportedResponses,
    getResponseHelp,
    getDetailedHelp,

    // Batch validation
    validateMultiple,
    getBestMatch,

    // Testing utilities
    testResponse,
    runTestSuite,

    // Analytics
    getValidationStats,

    // Constants
    responseTypes: ["yes", "no", "maybe", "unknown", "invalid"] as const,
    confidenceThresholds: {
      high: 0.8,
      medium: 0.6,
      low: 0.4
    } as const
  };
};

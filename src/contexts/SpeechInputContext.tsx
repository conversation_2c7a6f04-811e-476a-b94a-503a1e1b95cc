import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
  type ReactNode,
} from "react";
import {
  transcriptionService,
  type TranscriptionEvent,
  normalize,
} from "../services/TranscriptionService";
import { log } from "../services/LogService";

// ========== IMPROVED TYPES ==========
export type GameResponseType = "yes" | "no" | "maybe" | "unknown" | "invalid";

export interface SpeechInputState {
  transcription: string | null;
  isListening: boolean;
  isProcessing: boolean;
  confidence: number;
  lastValidatedResponse: GameResponseType | null;
  errorMessage: string | null;
}

export interface SpeechInputContextProps {
  // Current state
  state: SpeechInputState;

  // Basic controls
  startListening: () => void;
  stopListening: () => void;
  clearTranscription: () => void;
  reset: () => void;

  // Testing & simulation
  simulateTranscription: (text: string) => void;

  // Game-specific functionality
  validateGameResponse: (text: string) => GameResponseType;
  waitForValidResponse: (timeout?: number) => Promise<GameResponseType>;
  waitForCustomResponse: (expectedResponses: string[], timeout?: number) => Promise<string>;

  // Event handling
  addEventListener: (callback: (event: TranscriptionEvent) => void) => () => void;

  // Utilities
  normalizeText: (text: string) => Promise<string>;
  getSupportedResponses: () => string[];
  getResponseHelp: () => string;

  // 🆕 NEW: Enhanced utilities
  getConfidenceScore: (text: string) => number;
  getAlternativeResponses: (text: string) => GameResponseType[];
  isResponseAmbiguous: (text: string) => boolean;
}

// ========== OPTIMIZED PATTERNS ==========
// 🔧 MEJORA: Pre-compilar patrones para mejor performance
class ResponsePatternMatcher {
  private patternMap = new Map<string, GameResponseType>();
  private fuzzyPatterns: Array<{ pattern: RegExp; type: GameResponseType; confidence: number }> = [];

  constructor() {
    // this.initializePatterns();
  }

  // private initializePatterns() {
  //   const patterns = {
  //     yes: [
  //       "sí", "si", "yes", "correcto", "afirmativo", "exacto", "cierto",
  //       "verdad", "por supuesto", "claro", "efectivamente", "así es", "obvio",
  //       "desde luego", "sin duda", "seguro", "definitivamente"
  //     ],
  //     no: [
  //       "no", "nope", "negativo", "incorrecto", "falso", "para nada",
  //       "jamás", "nunca", "de ninguna manera", "en absoluto", "ni hablar",
  //       "imposible", "ni de coña", "qué va"
  //     ],
  //     maybe: [
  //       "tal vez", "quizás", "puede ser", "posible", "posiblemente",
  //       "probablemente", "es posible", "podría ser", "a veces", "depende",
  //       "más o menos", "regular", "ni sí ni no", "50/50"
  //     ],
  //     unknown: [
  //       "no sé", "no lo sé", "no tengo idea", "desconozco", "ni idea",
  //       "no estoy seguro", "no sabría decir", "no tengo ni idea",
  //       "ni la menor idea", "vete a saber", "quién sabe"
  //     ]
  //   };

  //   // Mapeo exacto para patrones comunes
  //   for (const [type, words] of Object.entries(patterns)) {
  //     words.forEach(word => {
  //       this.patternMap.set(word, type as GameResponseType);
  //     });
  //   }

  //   // Patrones fuzzy con regex para casos más complejos
  //   this.fuzzyPatterns = [
  //     { pattern: /^(sí|si|yes)(\s|$)/i, type: "yes", confidence: 0.9 },
  //     { pattern: /^no(\s|$)/i, type: "no", confidence: 0.9 },
  //     { pattern: /(no\s*(lo\s*)?sé|ni\s*idea)/i, type: "unknown", confidence: 0.8 },
  //     { pattern: /(tal\s*vez|quizás?)/i, type: "maybe", confidence: 0.8 },
  //   ];
  // }

  validate(text: string): { type: GameResponseType; confidence: number; alternatives: GameResponseType[] } {
    if (!text?.trim()) return { type: "invalid", confidence: 0, alternatives: [] };

    const normalized = text.toLowerCase().trim();

    // 1. Búsqueda exacta (más rápida)
    const exactMatch = this.patternMap.get(normalized);
    if (exactMatch) {
      return { type: exactMatch, confidence: 1.0, alternatives: [] };
    }

    // 2. Búsqueda fuzzy
    const matches: Array<{ type: GameResponseType; confidence: number }> = [];

    for (const { pattern, type, confidence } of this.fuzzyPatterns) {
      if (pattern.test(normalized)) {
        matches.push({ type, confidence });
      }
    }

    // 3. Búsqueda por inclusión (menos precisa)
    for (const [word, type] of this.patternMap.entries()) {
      if (normalized.includes(word)) {
        matches.push({ type, confidence: 0.6 });
      }
    }

    if (matches.length === 0) {
      return { type: "invalid", confidence: 0, alternatives: [] };
    }

    // Ordenar por confianza y retornar el mejor match
    matches.sort((a, b) => b.confidence - a.confidence);
    const bestMatch = matches[0];
    const alternatives = matches.slice(1).map(m => m.type);

    return {
      type: bestMatch.type,
      confidence: bestMatch.confidence,
      alternatives: Array.from(new Set(alternatives))
    };
  }
}

// ========== CONTEXT ==========
const SpeechInputContext = createContext<SpeechInputContextProps | undefined>(undefined);

export const useSpeechInput = () => {
  const context = useContext(SpeechInputContext);
  if (!context) {
    throw new Error("useSpeechInput must be used within SpeechInputProvider");
  }
  return context;
};

// ========== IMPROVED PROVIDER ==========
export const SpeechInputProvider = ({ children }: { children: ReactNode }) => {
  // ========== STATE ==========
  const [state, setState] = useState<SpeechInputState>({
    transcription: null,
    isListening: false,
    isProcessing: false,
    confidence: 0,
    lastValidatedResponse: null,
    errorMessage: null,
  });

  // ========== REFS ==========
  const abortControllerRef = useRef<AbortController | null>(null);
  const patternMatcherRef = useRef(new ResponsePatternMatcher());

  // ========== MEMOIZED VALUES ==========
  const supportedResponses = useMemo(() => [
    "Sí / No",
    "Tal vez / Quizás",
    "No lo sé / No sé",
    "Es posible / Puede ser"
  ], []);

  const responseHelp = useMemo(() =>
    "Puedes responder con: Sí, No, Tal vez, No lo sé, o sus variaciones"
  , []);

  // ========== IMPROVED EVENT HANDLING ==========
  const handleTranscriptionEvent = useCallback((event: TranscriptionEvent) => {
    // log.debug("speechInput", `📝 Evento recibido: ${event.type}`, {
    //   data: event.data.substring(0, 50),
    // });

    switch (event.type) {
      case "transcription": {
        const transcriptionText = event.normalized || event.data;
        const validation = patternMatcherRef.current.validate(transcriptionText);

        setState(prev => ({
          ...prev,
          transcription: transcriptionText,
          isProcessing: false,
          errorMessage: null,
          lastValidatedResponse: validation.type,
          confidence: validation.confidence
        }));
        break;
      }

      case "error":
        setState(prev => ({
          ...prev,
          isProcessing: false,
          errorMessage: event.data,
        }));
        break;

      case "command":
        // log.info("speechInput", `🎯 Comando ejecutado: ${event.data}`);
        break;
    }
  }, []);

  // ========== EFFECTS ==========
  useEffect(() => {
    // log.info("speechInput", "🎤 Inicializando SpeechInputProvider");

    // 🔧 MEJORA: Eliminar polling, usar solo eventos
    const removeListener = transcriptionService.addEventListener(handleTranscriptionEvent);

    // Sincronizar estado inicial
    const initialListening = transcriptionService.isCurrentlyListening();
    setState(prev => ({ ...prev, isListening: initialListening }));

    return () => {
      // log.info("speechInput", "🧹 Limpiando SpeechInputProvider");
      removeListener();

      // Cancelar operaciones pendientes
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [handleTranscriptionEvent]);

  // ========== BASIC CONTROLS ==========
  const startListening = useCallback(() => {
    // log.info("speechInput", "🎤 Iniciando escucha");

    setState(prev => ({ ...prev, isProcessing: true, errorMessage: null }));

    try {
      transcriptionService.startListening();
      setState(prev => ({ ...prev, isListening: true, isProcessing: false }));
    } catch (error) {
      // log.error("speechInput", "❌ Error iniciando escucha", error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        errorMessage: "Error al iniciar el micrófono"
      }));
    }
  }, []);

  const stopListening = useCallback(() => {
    // log.info("speechInput", "🛑 Deteniendo escucha");

    try {
      transcriptionService.stopListening();
      setState(prev => ({ ...prev, isListening: false }));
    } catch (error) {
      // log.error("speechInput", "❌ Error deteniendo escucha", error);
    }
  }, []);

  const clearTranscription = useCallback(() => {
    // log.debug("speechInput", "🧹 Limpiando transcripción");

    transcriptionService.clearTranscription();
    setState(prev => ({
      ...prev,
      transcription: null,
      lastValidatedResponse: null,
      errorMessage: null,
      confidence: 0
    }));
  }, []);

  const reset = useCallback(() => {
    // log.info("speechInput", "🔄 Reseteando SpeechInput");

    // Cancelar operaciones pendientes
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    transcriptionService.reset();
    setState({
      transcription: null,
      isListening: false,
      isProcessing: false,
      confidence: 0,
      lastValidatedResponse: null,
      errorMessage: null,
    });
  }, []);

  // ========== TESTING & SIMULATION ==========
  const simulateTranscription = useCallback((text: string) => {
    // log.debug("speechInput", `🎭 Simulando transcripción: ${text}`);
    transcriptionService.simulateTranscription(text);
  }, []);

  // ========== IMPROVED GAME-SPECIFIC FUNCTIONALITY ==========
  const validateGameResponse = useCallback((text: string): GameResponseType => {
    const result = patternMatcherRef.current.validate(text);
    return result.type;
  }, []);

  const getConfidenceScore = useCallback((text: string): number => {
    const result = patternMatcherRef.current.validate(text);
    return result.confidence;
  }, []);

  const getAlternativeResponses = useCallback((text: string): GameResponseType[] => {
    const result = patternMatcherRef.current.validate(text);
    return result.alternatives;
  }, []);

  const isResponseAmbiguous = useCallback((text: string): boolean => {
    const result = patternMatcherRef.current.validate(text);
    return result.alternatives.length > 0 && result.confidence < 0.8;
  }, []);

  // ========== IMPROVED ASYNC OPERATIONS ==========
  const waitForValidResponse = useCallback((timeout: number = 30000): Promise<GameResponseType> => {
    return new Promise((resolve, reject) => {
      // log.info("speechInput", `⏳ Esperando respuesta válida (timeout: ${timeout}ms)`);

      // 🔧 MEJORA: Usar AbortController para mejor cleanup
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      let resolved = false;

      const handleTranscription = (event: TranscriptionEvent) => {
        if (resolved || abortController.signal.aborted || event.type !== "transcription") return;

        const validation = patternMatcherRef.current.validate(event.normalized || event.data);

        if (validation.type !== "invalid" && validation.confidence > 0.6) {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();

          // log.success("speechInput", `✅ Respuesta válida recibida: ${validation.type} (${validation.confidence})`);
          resolve(validation.type);
        } else {
          // log.debug("speechInput", `⚠️ Respuesta inválida ignorada: ${event.data} (confidence: ${validation.confidence})`);
        }
      };

      const removeListener = transcriptionService.addEventListener(handleTranscription);

      const timeoutId = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          abortController.abort();
          removeListener();
          // log.warn("speechInput", "⏰ Timeout esperando respuesta válida");
          reject(new Error("Timeout esperando respuesta válida"));
        }
      }, timeout);

      // Cleanup en abort
      abortController.signal.addEventListener('abort', () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();
          reject(new Error("Operation aborted"));
        }
      });
    });
  }, []);

  const waitForCustomResponse = useCallback((
    expectedResponses: string[],
    timeout: number = 30000
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      // log.info("speechInput", `⏳ Esperando respuestas específicas`, { expectedResponses });

      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      let resolved = false;

      const handleTranscription = (event: TranscriptionEvent) => {
        if (resolved || abortController.signal.aborted || event.type !== "transcription") return;

        const normalized = event.normalized || event.data;
        const matchesExpected = expectedResponses.length === 0 ||
          expectedResponses.some(expected =>
            normalized.toLowerCase().includes(expected.toLowerCase())
          );

        if (matchesExpected) {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();

          // log.success("speechInput", `✅ Respuesta esperada recibida: ${normalized}`);
          resolve(normalized);
        } else {
          // log.debug("speechInput", `⚠️ Respuesta no esperada: ${normalized}`);
        }
      };

      const removeListener = transcriptionService.addEventListener(handleTranscription);

      const timeoutId = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          abortController.abort();
          removeListener();
          // log.warn("speechInput", "⏰ Timeout esperando respuesta específica");
          reject(new Error("Timeout esperando respuesta específica"));
        }
      }, timeout);

      abortController.signal.addEventListener('abort', () => {
        if (!resolved) {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();
          reject(new Error("Operation aborted"));
        }
      });
    });
  }, []);

  // ========== EVENT HANDLING ==========
  const addEventListener = useCallback((callback: (event: TranscriptionEvent) => void) => {
    return transcriptionService.addEventListener(callback);
  }, []);

  // ========== UTILITIES ==========
  const normalizeText = useCallback(async (text: string): Promise<string> => {
    return await normalize(text);
  }, []);

  const getSupportedResponses = useCallback((): string[] => {
    return supportedResponses;
  }, [supportedResponses]);

  const getResponseHelp = useCallback((): string => {
    return responseHelp;
  }, [responseHelp]);

  // ========== CONTEXT VALUE ==========
  const contextValue: SpeechInputContextProps = {
    // Current state
    state,

    // Basic controls
    startListening,
    stopListening,
    clearTranscription,
    reset,

    // Testing & simulation
    simulateTranscription,

    // Game-specific functionality
    validateGameResponse,
    waitForValidResponse,
    waitForCustomResponse,

    // Event handling
    addEventListener,

    // Utilities
    normalizeText,
    getSupportedResponses,
    getResponseHelp,

    // 🆕 NEW: Enhanced utilities
    getConfidenceScore,
    getAlternativeResponses,
    isResponseAmbiguous,
  };

  return (
    <SpeechInputContext.Provider value={contextValue}>
      {children}
    </SpeechInputContext.Provider>
  );
};

// contexts/GameOrchestratorContext.tsx
import {
  createContext,
  useContext,
  useCallback,
  useEffect,
  type ReactNode,
  useState,
} from "react";
import { useEnygmaGame } from "./EnygmaGameContext";
import { useSpeechInput } from "./SpeechInputContext";
import { useSpeechOutput } from "./SpeechOutputContext";
import { useMovistarPlusContext } from "./MovistarPlusContext";
import { log } from "../services/LogService";

export type GameFlowState =
  | "idle"
  | "initializing"
  | "waiting_for_user_choice"
  | "game_active"
  | "processing_response"
  | "showing_results"
  | "error";

interface GameOrchestratorContextProps {
  // Overall state
  flowState: GameFlowState;
  isProcessing: boolean;
  error: string | null;

  // Main game flow
  initializeApp: () => Promise<void>;
  startGameFlow: (mode: "player_vs_ia" | "ia_vs_player") => Promise<void>;
  handleUserInteraction: (
    input: string,
    type: "speech" | "text"
  ) => Promise<void>;
  endGameFlow: () => Promise<void>;

  // Coordinated actions
  speakAndWaitForResponse: (
    message: string,
    expectedResponses?: string[]
  ) => Promise<string>;
  processGameTurn: (userInput: string) => Promise<void>;
  handleGameEnd: (result: any) => Promise<void>;

  // Game announcements
  announceGameStart: (mode: "player_vs_ia" | "ia_vs_player") => Promise<void>;
  announceGameEnd: (result: "win" | "lose" | "draw", character?: string) => Promise<void>;

  // Error recovery
  recoverFromError: () => void;
  retryLastAction: () => Promise<void>;

  // App state
  isReady: boolean;
  setupProgress: number; // 0-100
}

const GameOrchestratorContext = createContext<
  GameOrchestratorContextProps | undefined
>(undefined);

export const useGameOrchestrator = () => {
  const context = useContext(GameOrchestratorContext);
  if (!context) {
    throw new Error(
      "useGameOrchestrator must be used within GameOrchestratorProvider"
    );
  }
  return context;
};

export const GameOrchestratorProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  // Context dependencies
  const game = useEnygmaGame();
  const speechInput = useSpeechInput();
  const speechOutput = useSpeechOutput();
  const movistar = useMovistarPlusContext();

  // Local state
  const [flowState, setFlowState] = useState<GameFlowState>("idle");
  const [isProcessing, setIsProcessing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [setupProgress, setSetupProgress] = useState<number>(0);
  const [lastAction, setLastAction] = useState<(() => Promise<void>) | null>(
    null
  );

  // Computed state
  const isReady =
    flowState !== "idle" && flowState !== "initializing" && !error;

  // Initialize the entire app
  const initializeApp = useCallback(async (): Promise<void> => {
    setFlowState("initializing");
    setSetupProgress(0);

    try {
      // log.info("orchestrator", "🚀 Initializing Enygma app");

      // Step 1: Configure speech output (25%)
      // log.info("orchestrator", "🔧 Configuring speech output");
      const speechConfigured = await speechOutput.configure("female");
      if (!speechConfigured) {
        throw new Error("Failed to configure speech output");
      }
      setSetupProgress(25);

      // Step 2: Start speech input (50%)
      // log.info("orchestrator", "🎤 Starting speech input");
      speechInput.startListening();
      setSetupProgress(50);

      // Step 3: Test AI connectivity (75%)
      // log.info("orchestrator", "🤖 Testing AI connectivity");
      // Could add a quick AI health check here
      setSetupProgress(75);

      // Step 4: Welcome message (100%)
      // log.info("orchestrator", "👋 Playing welcome message");
      await speechOutput.speakGameMessage(
        "¡Bienvenido a Enygma! Versión mejorada de akinator, nada que ver, mejor, más potente, más personajes... ¿Estás listo para empezar?",
        "system"
      );
      setSetupProgress(100);

      setFlowState("waiting_for_user_choice");
      // log.success("orchestrator", "✅ App initialized successfully");
    } catch (error) {
      // log.error("orchestrator", "❌ Failed to initialize app", error);
      setError(
        error instanceof Error ? error.message : "Unknown initialization error"
      );
      setFlowState("error");
    }
  }, [speechOutput, speechInput]);

  // Announce game start
  const announceGameStart = useCallback(
    async (mode: "player_vs_ia" | "ia_vs_player"): Promise<void> => {
      try {
        // log.info("orchestrator", `🎮 Announcing game start: ${mode}`);

        const announcement =
          mode === "player_vs_ia"
            ? "¡Perfecto! Piensa en un personaje del entretenimiento. Yo haré preguntas y tú respondes con Sí, No, Tal vez o No lo sé."
            : "¡Genial! He pensado en un personaje. Haz preguntas que pueda responder con Sí, No, Tal vez o No lo sé.";

        // Add the announcement message to the game session
        if (game.session) {
          const initialMessage = {
            id: `msg-${Date.now()}`,
            text: announcement,
            sender: "ai" as const,
            timestamp: new Date(),
            type: "system" as const,
          };

          game.session.messages = [initialMessage];
        }

        await speechOutput.speakGameMessage(announcement, "system");
        // log.success("orchestrator", "✅ Game start announced");
      } catch (error) {
        // log.error("orchestrator", "❌ Failed to announce game start", error);
        throw error;
      }
    },
    [game, speechOutput]
  );

  // Announce game end
  const announceGameEnd = useCallback(
    async (result: "win" | "lose" | "draw", character?: string): Promise<void> => {
      try {
        // log.info("orchestrator", `🏁 Announcing game end: ${result}`, { character });

        let resultMessage = "";
        if (result === "win") {
          resultMessage = character
            ? `¡Excelente! Adiviné correctamente. El personaje era ${character}. `
            : `¡Felicidades! Has adivinado correctamente. `;
        } else if (result === "lose") {
          resultMessage = `Se acabaron las preguntas. `;
        } else {
          resultMessage = `Empate - ¡Buen juego! `;
        }

        resultMessage += "¿Te gustaría ver contenido relacionado en Movistar Plus?";

        await speechOutput.speakGameMessage(resultMessage, "victory");
        // log.success("orchestrator", "✅ Game end announced");
      } catch (error) {
        // log.error("orchestrator", "❌ Failed to announce game end", error);
        throw error;
      }
    },
    [speechOutput]
  );

  // Start the game flow
  const startGameFlow = useCallback(
    async (mode: "player_vs_ia" | "ia_vs_player"): Promise<void> => {
      setFlowState("initializing");
      setIsProcessing(true);

      try {
        // log.info("orchestrator", `🎮 Starting game flow: ${mode}`);

        // Start the game session
        await game.startNewGame(mode);

        // Announce game start
        await announceGameStart(mode);

        setFlowState("game_active");
        // log.success("orchestrator", "✅ Game flow started");
      } catch (error) {
        // log.error("orchestrator", "❌ Failed to start game", error);
        setError(
          error instanceof Error ? error.message : "Failed to start game"
        );
        setFlowState("error");
      } finally {
        setIsProcessing(false);
      }
    },
    [game, announceGameStart]
  );

  // Handle user interaction (speech or text)
  const handleUserInteraction = useCallback(
    async (input: string, type: "speech" | "text"): Promise<void> => {
      if (flowState !== "game_active" || isProcessing) {
        // log.warn(
        //   "orchestrator",
        //   "Cannot handle user interaction in current state",
        //   { flowState, isProcessing }
        // );
        return;
      }

      setIsProcessing(true);
      setFlowState("processing_response");

      const action = async () => {
        try {
          // log.info("orchestrator", `👤 Processing ${type} input: ${input}`);

          // Validate input first
          const validation = game.validateUserInput(input);
          if (!validation.isValid) {
            await speechOutput.speakGameMessage(
              validation.suggestion ||
                "No entendí esa respuesta. Inténtalo de nuevo.",
              "hint"
            );
            return;
          }

          // Process the game turn
          await processGameTurn(input);

          // Check if game ended
          if (game.session?.phase === "finished") {
            await handleGameEnd(game.session);
          } else {
            setFlowState("game_active");
          }
        } catch (error) {
          // log.error(
          //   "orchestrator",
          //   "❌ Failed to process user interaction",
          //   error
          // );
          await speechOutput.speakGameMessage(
            "Lo siento, hubo un error. ¿Puedes repetir?",
            "hint"
          );
          setFlowState("game_active");
        }
      };

      setLastAction(() => action);
      await action();
      setIsProcessing(false);
    },
    [flowState, isProcessing, game, speechOutput]
  );

  // Coordinated speak and wait
  const speakAndWaitForResponse = useCallback(
    async (message: string, expectedResponses?: string[]): Promise<string> => {
      // log.info("orchestrator", "🗣️ Speaking and waiting for response", {
      //   message: message.substring(0, 50),
      // });

      // Speak the message
      await speechOutput.speak(message);

      // 🔧 FIX: Usar waitForValidResponse en lugar de waitForResponse
      if (expectedResponses && expectedResponses.length > 0) {
        // Si hay respuestas específicas esperadas, usar waitForCustomResponse
        const response =
          await speechInput.waitForCustomResponse(expectedResponses);
        // log.info("orchestrator", "👂 Received specific response", { response });
        return response;
      } else {
        // Si no hay respuestas específicas, esperar cualquier respuesta válida del juego
        const response = await speechInput.waitForValidResponse();
        // log.info("orchestrator", "👂 Received valid game response", {
        //   response,
        // });
        return response.toString(); // Convertir GameResponseType a string
      }
    },
    [speechOutput, speechInput]
  );

  // Process a game turn
  const processGameTurn = useCallback(
    async (userInput: string): Promise<void> => {
      if (!game.session) return;

      // log.info("orchestrator", "⚙️ Processing game turn", { input: userInput });

      if (game.playerRole === "guesser") {
        // User is asking a question
        await game.askQuestion(userInput);

        // Speak the AI's response
        const lastMessage =
          game.session.messages[game.session.messages.length - 1];
        if (lastMessage && lastMessage.sender === "ai") {
          await speechOutput.speakGameMessage(
            lastMessage.text,
            lastMessage.type
          );
        }
      } else {
        // User is answering a question
        const validatedResponse = speechInput.validateGameResponse(userInput);
        if (validatedResponse !== "invalid") {
          await game.respondToQuestion(validatedResponse);

          // Speak the AI's next question or guess
          const lastMessage =
            game.session.messages[game.session.messages.length - 1];
          if (lastMessage && lastMessage.sender === "ai") {
            const messageType =
              lastMessage.type === "guess" ? "guess" : "question";
            await speechOutput.speakGameMessage(lastMessage.text, messageType);
          }
        }
      }
    },
    [game, speechOutput, speechInput]
  );

  // Handle game end
  const handleGameEnd = useCallback(
    async (session: any): Promise<void> => {
      setFlowState("showing_results");

      try {
        // log.info("orchestrator", "🏁 Handling game end", {
        //   winner: session.winner,
        // });

        // Determine result and character
        let result: "win" | "lose" | "draw" = "draw";
        let character: string | undefined;

        if (session.winner === "ai") {
          result = "win";
          character = session.finalGuess;
        } else if (session.winner === "user") {
          result = "win";
          character = session.currentCharacter;
        } else {
          result = "lose";
          character = session.finalGuess || session.currentCharacter;
        }

        // Announce game end
        await announceGameEnd(result, character);

        // Search for related content if there's a character
        if (character) {
          // log.info("orchestrator", "🔍 Searching Movistar Plus content", {
          //   character,
          // });

          try {
            await movistar.searchByTitle(character);
          } catch (error) {
            // log.warn(
            //   "orchestrator",
            //   "Failed to search Movistar content",
            //   error
            // );
          }
        }
      } catch (error) {
        // log.error("orchestrator", "❌ Failed to handle game end", error);
      }
    },
    [announceGameEnd, movistar]
  );

  // End game flow
  const endGameFlow = useCallback(async (): Promise<void> => {
    // log.info("orchestrator", "🛑 Ending game flow");

    try {
      speechInput.stopListening();
      speechInput.clearTranscription();

      await speechOutput.speakGameMessage(
        "¡Gracias por jugar a Enygma! ¿Te gustaría jugar otra partida?",
        "system"
      );

      setFlowState("waiting_for_user_choice");
    } catch (error) {
      // log.error("orchestrator", "❌ Failed to end game flow", error);
    }
  }, [speechInput, speechOutput]);

  // Error recovery
  const recoverFromError = useCallback(() => {
    // log.info("orchestrator", "🔄 Recovering from error");
    setError(null);
    setFlowState("waiting_for_user_choice");
  }, []);

  // Retry last action
  const retryLastAction = useCallback(async (): Promise<void> => {
    if (lastAction) {
      // log.info("orchestrator", "🔄 Retrying last action");
      try {
        await lastAction();
        setError(null);
      } catch (error) {
        // log.error("orchestrator", "❌ Retry failed", error);
      }
    }
  }, [lastAction]);

  // Auto-initialize on mount
  useEffect(() => {
    if (flowState === "idle") {
      initializeApp();
    }
  }, [initializeApp, flowState]);

  // Listen for transcription events
  useEffect(() => {
    const removeListener = speechInput.addEventListener?.((event: any) => {
      if (
        event.type === "transcription" &&
        event.normalized &&
        flowState === "game_active"
      ) {
        handleUserInteraction(event.normalized, "speech");
      }
    });

    return removeListener;
  }, [speechInput, handleUserInteraction, flowState]);

  return (
    <GameOrchestratorContext.Provider
      value={{
        flowState,
        isProcessing,
        error,
        initializeApp,
        startGameFlow,
        handleUserInteraction,
        endGameFlow,
        speakAndWaitForResponse,
        processGameTurn,
        handleGameEnd,
        announceGameStart,
        announceGameEnd,
        recoverFromError,
        retryLastAction,
        isReady,
        setupProgress,
      }}
    >
      {children}
    </GameOrchestratorContext.Provider>
  );
};

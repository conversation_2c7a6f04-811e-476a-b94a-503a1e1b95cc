import { useState, useEffect, useCallback } from "react";
// Components
import { Loader } from "./components/Loader";
import { Header } from "./components/Header/Header";
import { GlobalLogs } from "./components/GlobalLogs";
import { CookieConsentBanner } from "./components/CookieConsentBanner";
import MainView from "./components/views/MainView";
import PlayView from "./components/views/PlayView";
import RulesView from "./components/views/RulesView";
import LivesView from "./components/views/LivesView";
import CluesView from "./components/views/CluesView";
// Types
import type { ViewMode } from "./models/general";
// Contexts
import { useAppContext } from "./contexts/AppContext";
import { useEnygmaGame } from "./contexts/EnygmaGameContext";
import { useGameOrchestrator } from "./contexts/GameOrchestratorContext";
import { useSpeechOutput } from "./contexts/SpeechOutputContext";
// Services
import type { GameMode } from "./services/AIService";
// Styles
import "./App.scss";
import { WelcomeScreen } from "./components/WelcomeScreen";

type AppState = "loading" | "consent" | "welcome" | "ready";

function App() {
  const { isInitialized, errors } = useAppContext();

  // 🆕 NUEVO: Estado de la aplicación
  const [appState, setAppState] = useState<AppState>("loading");
  const [currentView, setCurrentView] = useState<ViewMode>("main");

  // Estados existentes
  const [audioActivated, setAudioActivated] = useState(false);
  const [hasPlayedWelcome, setHasPlayedWelcome] = useState<boolean>(false);
  const [isStartingGame, setIsStartingGame] = useState<boolean>(false);

  // Contexts
  const {} = useEnygmaGame();
  const { startGameFlow, isReady, setupProgress } = useGameOrchestrator();
  const {
    speakGameMessage,
    state: { isReady: speechReady, isConfiguring },
  } = useSpeechOutput();

  // ========== EFECTOS ==========
  useEffect(() => {
    if (isInitialized) {
      // Verificar estado inicial de la app
      const hasConsent = localStorage.getItem("enygma_analytics_consent");
      const hasAudioActivated = localStorage.getItem("enygma_audio_activated");
      console.log('AppState - hasConsent:', hasConsent);
      console.log('AppState - hasAudioActivated:', hasAudioActivated);

      if (!hasConsent) {
        console.log('AppState - consent');
        setAppState("consent"); // Mostrar banner de consentimiento
      } else if (!hasAudioActivated && !speechReady) {
        console.log('AppState - welcome');
        setAppState("welcome"); // Mostrar pantalla de bienvenida
      } else {
        console.log('AppState - ready');
        setAppState("ready"); // Ir directo al juego
        setHasPlayedWelcome(true);
      }
    }
  }, [isInitialized, speechReady]);

  useEffect(() => {
    if (errors.length > 0) {
      console.warn("⚠️ App: Errores detectados:", errors);
    }
  }, [errors]);

  // ========== HANDLERS ==========

  // 🔧 MODIFICADO: Handler cuando se acepta el consentimiento
  const handleAudioActivated = useCallback(() => {
    setAudioActivated(true);

    // Si ya tiene audio configurado, ir directo a ready
    if (speechReady) {
      setAppState("ready");
      setHasPlayedWelcome(true);
    } else {
      // Si no, mostrar pantalla de bienvenida
      setAppState("welcome");
    }
  }, [speechReady]);

  // 🆕 NUEVO: Handler cuando se completa la pantalla de bienvenida
  const handleWelcomeComplete = useCallback(() => {
    setAppState("ready");
    setHasPlayedWelcome(true);
  }, []);

  // Handlers existentes (sin cambios)
  const handleStartGame = async (mode: GameMode) => {
    setIsStartingGame(true);

    try {
      if (!hasPlayedWelcome) {
        await speakGameMessage("Bienvenido a Enygma, ¿lo adivinas?", "system");
        setHasPlayedWelcome(true);
      }

      await startGameFlow(mode);
      setCurrentView("play");
    } catch (error) {
      console.error("Error al iniciar el juego:", error);
    } finally {
      setIsStartingGame(false);
    }
  };

  const handleToggleSound = () => {
    console.log("Toggle sound");
  };

  const handleGoHome = () => {
    console.log("Go to home");
  };

  const handleShowRules = () => {
    setCurrentView("rules");
  };

  const handleShowLives = () => {
    setCurrentView("lives");
  };

  const handleShowClues = () => {
    setCurrentView("clues");
  };

  const handleExistGame = () => {
    setCurrentView("main");
  };

  const handleBackToMain = () => {
    setCurrentView("main");
  };

  // ========== RENDERIZADO CONDICIONAL ==========

  // Loading state
  if (appState === "loading" || (!isReady && setupProgress < 100)) {
    return <Loader text={`Inicializando aplicación... ${setupProgress}%`} />;
  }

  // Consent state - mostrar banner de consentimiento
  if (appState === "consent") {
    return (
      <div className="App">
        <CookieConsentBanner onAudioActivated={handleAudioActivated} />

        {/* Fondo básico mientras se muestra el banner */}
        <div className="game-container">
          <img src="assets/game/background.png" alt="Background" className="background" />
        </div>
      </div>
    );
  }

  // Welcome state - mostrar pantalla de bienvenida
  if (appState === "welcome") {
    return (
      <div className="App">
        <div className="game-container">
          <img src="assets/game/background.png" alt="Background" className="background" />
          <WelcomeScreen onGameReady={handleWelcomeComplete} />
        </div>
      </div>
    );
  }

  // Ready state - aplicación principal
  const renderContent = () => {
    switch (currentView) {
      case "main":
        return (
          <MainView
            handleStartGame={handleStartGame}
            handleShowRules={handleShowRules}
            isStartingGame={isStartingGame}
            isReady={isReady}
          />
        );
      case "play":
        return (
          <PlayView
            handleShowLives={handleShowLives}
            handleShowClues={handleShowClues}
            handleExistGame={handleExistGame}
          />
        );
      case "rules":
        return <RulesView isOpen={true} onClose={handleBackToMain} />;
      case "lives":
        return <LivesView />;
      case "clues":
        return <CluesView />;
      default:
        return <div className="content"></div>;
    }
  };

  return (
    <div className="App">
      <div className="game-container">
        <img src="assets/game/background.png" alt="Background" className="background" />

        <div className="board">
          <Header
            currentView={currentView}
            onBackToMain={handleBackToMain}
            onToggleSound={handleToggleSound}
            onGoHome={handleGoHome}
            showBackButton={currentView !== "main" && currentView !== "play"}
          />

          {renderContent()}
        </div>
      </div>

      {import.meta.env.VITE_DEBUG === "true" && (
        <GlobalLogs
          maxHeight="400px"
          position="fixed"
          defaultVisible={false}
        />
      )}
    </div>
  );
}

export default App;

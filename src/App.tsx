import { useState, useEffect, useCallback } from "react";
// Components
import { Loader } from "./components/Loader";
import { Header } from "./components/Header/Header";
import { GlobalLogs } from "./components/GlobalLogs";
import { CookieConsentBanner } from "./components/CookieConsentBanner";
import MainView from "./components/views/MainView";
import PlayView from "./components/views/PlayView";
import RulesView from "./components/views/RulesView";
import LivesView from "./components/views/LivesView";
import CluesView from "./components/views/CluesView";
// Types
import type { ViewMode } from "./models/general";
// Contexts
import { useAppContext } from "./contexts/AppContext";
import { useEnygmaGame } from "./contexts/EnygmaGameContext";
import { useGameOrchestrator } from "./contexts/GameOrchestratorContext";
import { useSpeechOutput } from "./contexts/SpeechOutputContext";
// Services
import type { GameMode } from "./services/AIService";
// Styles
import "./App.scss";
import { AudioDebugPanel } from "./components/Debug/AudioDebugPanel";
import { Banner } from "microapps";

function App() {
  const { isInitialized, errors } = useAppContext();
  const [, setAudioActivated] = useState(false);

  // 🔧 CAMBIO: Usar los nuevos contextos
  const {} = useEnygmaGame();
  const { startGameFlow, isReady, setupProgress } = useGameOrchestrator();
  const {
    speakGameMessage,
    playBackgroundMusic,
    pauseMusic,
    resumeMusic,
    stopMusic,
    toggleMute,
    state: { isReady: speechReady, isConfiguring, isMusicPlaying, audioState },
  } = useSpeechOutput();

  const [hasPlayedWelcome, setHasPlayedWelcome] = useState<boolean>(false);
  const [isStartingGame, setIsStartingGame] = useState<boolean>(false);
  const [showAudioBanner, setShowAudioBanner] = useState<boolean>(false);
  const [musicInitialized, setMusicInitialized] = useState<boolean>(false);
  const [userHasInteracted, setUserHasInteracted] = useState<boolean>(false); // 🆕

  // 🆕 Estado para controlar qué vista mostrar
  const [currentView, setCurrentView] = useState<ViewMode>("main");

  // ========== EFECTOS ==========
  useEffect(() => {
    if (isInitialized) {
      console.log("✅ App: Aplicación inicializada correctamente");
    }
  }, [isInitialized]);

  // Mostrar errores si los hay
  useEffect(() => {
    if (errors.length > 0) {
      console.warn("⚠️ App: Errores detectados:", errors);
    }
  }, [errors]);

  // 🆕 DETECTAR PRIMERA INTERACCIÓN DEL USUARIO
  useEffect(() => {
    const handleFirstInteraction = () => {
      setUserHasInteracted(true);
      console.log("🎯 Primera interacción del usuario detectada");

      // Remover listeners después de la primera interacción
      ['click', 'touchstart', 'keydown'].forEach(event => {
        document.removeEventListener(event, handleFirstInteraction);
      });
    };

    // Escuchar primera interacción
    ['click', 'touchstart', 'keydown'].forEach(event => {
      document.addEventListener(event, handleFirstInteraction, { once: true });
    });

    return () => {
      ['click', 'touchstart', 'keydown'].forEach(event => {
        document.removeEventListener(event, handleFirstInteraction);
      });
    };
  }, []);

  // Verificar si se necesita mostrar banner de audio
  useEffect(() => {
    const hasAudioActivated = localStorage.getItem("enygma_audio_activated");
    const hasConsent = localStorage.getItem("enygma_analytics_consent");

    if (hasAudioActivated === "true") {
      setHasPlayedWelcome(true);
      setShowAudioBanner(false);
      setUserHasInteracted(true); // 🆕 Si ya está activado, asumir interacción
    } else if (!hasConsent) {
      setShowAudioBanner(false);
    } else {
      setShowAudioBanner(true);
    }
  }, []);

  // 🔧 MEJORADO: INICIALIZAR MÚSICA SOLO DESPUÉS DE INTERACCIÓN
  useEffect(() => {
    const initializeBackgroundMusic = async () => {
      // 🔧 CAMBIO: Solo intentar si el usuario ha interactuado
      if (
        currentView === "main" &&
        speechReady &&
        !musicInitialized &&
        hasPlayedWelcome &&
        userHasInteracted &&
        !audioState.isMuted
      ) {
        try {
          console.log("🎵 Iniciando música de fondo después de interacción...");
          await playBackgroundMusic();
          setMusicInitialized(true);
          console.log("✅ Música de fondo iniciada correctamente");
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.warn("⚠️ Error iniciando música de fondo (esperado si no hay interacción):", errorMessage);
          // No es crítico, continuar sin música hasta que haya interacción
        }
      }
    };

    initializeBackgroundMusic();
  }, [
    currentView,
    speechReady,
    musicInitialized,
    hasPlayedWelcome,
    userHasInteracted,
    audioState.isMuted,
    playBackgroundMusic
  ]);

  // 🆕 RETRY MÚSICA CUANDO EL USUARIO INTERACTÚE
  useEffect(() => {
    const retryMusicAfterInteraction = async () => {
      if (
        userHasInteracted &&
        !musicInitialized &&
        hasPlayedWelcome &&
        currentView === "main" &&
        speechReady &&
        !audioState.isMuted
      ) {
        try {
          console.log("🔄 Reintentando música después de interacción...");
          await playBackgroundMusic();
          setMusicInitialized(true);
          console.log("✅ Música iniciada tras interacción");
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          console.warn("⚠️ Aún no se puede reproducir música:", errorMessage);
        }
      }
    };

    if (userHasInteracted) {
      retryMusicAfterInteraction();
    }
  }, [
    userHasInteracted,
    musicInitialized,
    hasPlayedWelcome,
    currentView,
    speechReady,
    audioState.isMuted,
    playBackgroundMusic
  ]);

  // ========== EFECTO PARA NARRACIÓN DE BIENVENIDA ==========
  useEffect(() => {
    const playWelcomeMessage = () => {
      if (hasPlayedWelcome || !isReady || !speechReady) {
        return;
      }

      console.log("🎵 Audio preparado - requiere interacción del usuario para reproducir");
    };

    const timer = setTimeout(playWelcomeMessage, 1000);
    return () => clearTimeout(timer);
  }, [hasPlayedWelcome, isReady, speechReady]);

  // ========== FUNCIONES DE AUDIO ==========
  const playAudio = useCallback(
    async (text: string) => {
      try {
        await speakGameMessage(text, "system");
      } catch (error) {
        console.error("❌ Error reproduciendo audio:", error);
        throw error;
      }
    },
    [speakGameMessage]
  );

  // 🔧 MEJORADO: Nueva función para activar audio con interacción del usuario
  const activateAudioWithWelcome = useCallback(async () => {
    try {
      // Marcar que el usuario ha interactuado
      setUserHasInteracted(true);

      // Reproducir mensaje de bienvenida
      await playAudio("Bienvenido a Enygma, ¿lo adivinas?");

      setHasPlayedWelcome(true);
      setShowAudioBanner(false);
      localStorage.setItem("enygma_audio_activated", "true");

      console.log("✅ Audio activado con mensaje de bienvenida");

      // Intentar iniciar música inmediatamente después del mensaje
      setTimeout(async () => {
        if (currentView === "main" && !musicInitialized && !audioState.isMuted) {
          try {
            await playBackgroundMusic();
            setMusicInitialized(true);
            console.log("✅ Música iniciada después del mensaje de bienvenida");
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.warn("⚠️ Música aún no puede iniciarse:", errorMessage);
          }
        }
      }, 2000); // Esperar 2 segundos tras el mensaje de bienvenida

    } catch (error) {
      console.error("❌ Error activando audio:", error);
    }
  }, [playAudio, currentView, musicInitialized, audioState.isMuted, playBackgroundMusic]);

  const handleStartGame = async (mode: GameMode) => {
    setIsStartingGame(true);

    try {
      // Activar audio con bienvenida si no se ha reproducido aún
      if (!hasPlayedWelcome) {
        await activateAudioWithWelcome();
      }

      await startGameFlow(mode);
      setCurrentView("play");
      console.log("🎮 Juego iniciado con modo:", mode);
    } catch (error) {
      console.error("Error al iniciar el juego:", error);
    } finally {
      setIsStartingGame(false);
    }
  };

  // 🆕 MANEJO MEJORADO DEL BOTÓN DE SONIDO
  const handleToggleSound = useCallback(() => {
    const wasMuted = audioState.isMuted;
    const newMuteState = toggleMute();

    console.log(`🔊 Audio ${newMuteState ? 'silenciado' : 'activado'}`);

    // Si se activa el audio y estamos en main, intentar iniciar música
    if (currentView === "main" && !newMuteState && !isMusicPlaying && userHasInteracted) {
      if (musicInitialized) {
        resumeMusic();
      } else {
        // Intentar inicializar música si no está inicializada
        playBackgroundMusic()
          .then(() => {
            setMusicInitialized(true);
            console.log("✅ Música iniciada desde botón de sonido");
          })
          .catch(error => {
            console.warn("⚠️ No se pudo iniciar música desde botón:", error.message);
          });
      }
    }
  }, [
    toggleMute,
    audioState.isMuted,
    currentView,
    isMusicPlaying,
    userHasInteracted,
    musicInitialized,
    resumeMusic,
    playBackgroundMusic
  ]);

  const handleGoHome = () => {
    console.log("Go to home");
    setCurrentView("main");

    // Reanudar música si estamos volviendo al main y no está reproduciéndose
    if (!isMusicPlaying && musicInitialized && !audioState.isMuted && userHasInteracted) {
      resumeMusic();
    }
  };

  const handleShowRules = () => {
    setCurrentView("rules");
  };

  const handleShowLives = () => {
    setCurrentView("lives");
  };

  const handleShowClues = () => {
    setCurrentView("clues");
  };

  const handleExistGame = () => {
    setCurrentView("main");

    // Reanudar música al salir del juego
    if (!isMusicPlaying && musicInitialized && !audioState.isMuted && userHasInteracted) {
      resumeMusic();
    }
  };

  const handleBackToMain = () => {
    setCurrentView("main");

    // Reanudar música al volver al main
    if (!isMusicPlaying && musicInitialized && !audioState.isMuted && userHasInteracted) {
      resumeMusic();
    }
  };

  // ========== HANDLERS ORIGINALES ==========
  const handleAudioActivated = () => {
    setAudioActivated(true);
    setUserHasInteracted(true); // 🆕 Marcar interacción desde cookies
    console.log("✅ Audio activado desde banner de cookies");
  };

  // EFECTO PARA CONTROLAR MÚSICA SEGÚN LA VISTA
  useEffect(() => {
    if (!musicInitialized || audioState.isMuted || !userHasInteracted) return;

    // Pausar música durante el juego, mantenerla en otras vistas
    if (currentView === "play") {
      if (isMusicPlaying) {
        pauseMusic();
        console.log("🎵 Música pausada durante el juego");
      }
    } else {
      // Reanudar música en vistas que no sean el juego
      if (!isMusicPlaying) {
        resumeMusic();
        console.log("🎵 Música reanudada en vista:", currentView);
      }
    }
  }, [
    currentView,
    musicInitialized,
    audioState.isMuted,
    isMusicPlaying,
    userHasInteracted,
    pauseMusic,
    resumeMusic
  ]);

  // ========== RENDERIZADO CONDICIONAL ==========
  if (!isReady && setupProgress < 100) {
    return <Loader text={`Inicializando aplicación... ${setupProgress}%`} />;
  }

  const renderContent = () => {
    switch (currentView) {
      case "main":
        return <MainView
          handleStartGame={handleStartGame}
          handleShowRules={handleShowRules}
          isStartingGame={isStartingGame}
          isReady={isReady}
        />;
      case "play":
        return <PlayView
          handleShowLives={handleShowLives}
          handleShowClues={handleShowClues}
          handleExistGame={handleExistGame}
        />;
      case "rules":
        return <RulesView isOpen={true} onClose={handleBackToMain} />;
      case "lives":
        return <LivesView />;
      case "clues":
        return <CluesView />;
      default:
        return <div className="content"></div>;
    }
  };

  return (
    <div className="App">
      <CookieConsentBanner onAudioActivated={handleAudioActivated} />

      <div className="game-container">
        <img src="assets/game/background.png" alt="Background" className="background" />

        <div className="board">
          <Header
            currentView={currentView}
            onBackToMain={handleBackToMain}
            onToggleSound={handleToggleSound}
            onGoHome={handleGoHome}
            showBackButton={currentView !== "main" && currentView !== "play"}
          />

          {renderContent()}
        </div>
      </div>

      {import.meta.env.VITE_DEBUG === "true" && (
        <GlobalLogs
          maxHeight="400px"
          position="fixed"
          defaultVisible={false}
        />
      )}
    </div>
  );
}

export default App;
